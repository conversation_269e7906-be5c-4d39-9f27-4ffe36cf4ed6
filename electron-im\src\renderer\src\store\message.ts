// 消息状态管理 - Pinia Store
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { wsService, WebSocketState } from '../services/websocketService'
import type { TextMessage, SystemMessage } from '../services/websocketService'
import { apiClient } from '../api'
import { useWebSocketStore } from './websocket'
import { dbManager } from '../db/db-manager'

// 消息接口定义
export interface Message {
  id: string
  senderId: string
  receiverId: string
  content: string
  timestamp: number
  type: number
  isRead?: boolean
  isSending?: boolean
  sendError?: string
}

// 聊天会话接口
export interface ChatSession {
  userId: string
  userName: string
  userAvatar: string
  lastMessage?: Message
  unreadCount: number
  lastActiveTime: number
}

// 聊天历史响应接口
export interface ChatHistoryResponse {
  success: boolean
  messages: Message[]
  pagination: {
    page: number
    limit: number
    pages: number
  }
}

export const useMessageStore = defineStore('message', () => {
  // 获取全局 WebSocket 状态
  const webSocketStore = useWebSocketStore()

  // 状态
  const messages = ref<Map<string, Message[]>>(new Map()) // 按用户ID分组的消息
  const chatSessions = ref<Map<string, ChatSession>>(new Map()) // 聊天会话
  const currentChatUserId = ref<string>('') // 当前聊天的用户ID
  const isLoading = ref(false)
  const error = ref<string>('')
  const paginationState = ref<Map<string, { currentPage: number; hasMore: boolean }>>(new Map()) // 分页状态

  // 计算属性
  const currentMessages = computed(() => {
    if (!currentChatUserId.value) return []
    return messages.value.get(currentChatUserId.value) || []
  })

  const sortedChatSessions = computed(() => {
    return Array.from(chatSessions.value.values()).sort(
      (a, b) => b.lastActiveTime - a.lastActiveTime
    )
  })

  const totalUnreadCount = computed(() => {
    return Array.from(chatSessions.value.values()).reduce(
      (total, session) => total + session.unreadCount,
      0
    )
  })

  // 初始化数据库
  const initDatabase = async () => {
    try {
      const currentUser = apiClient.getCurrentUser()
      if (!currentUser) {
        throw new Error('用户未登录，无法初始化数据库')
      }

      console.log('MessageStore - 开始初始化数据库')
      await dbManager.initialize(currentUser.id)
      console.log('MessageStore - 数据库初始化成功')
    } catch (err) {
      console.error('数据库初始化失败:', err)
      error.value = err instanceof Error ? err.message : '数据库初始化失败'
    }
  }

  // 初始化WebSocket连接
  const initWebSocket = async (token?: string) => {
    try {
      console.log('MessageStore - 开始初始化WebSocket连接')

      // 先初始化数据库
      await initDatabase()

      // 设置WebSocket事件监听
      wsService.on('onStateChange', (state) => {
        // 状态变化时同步到全局 store
        webSocketStore.setState(state)
        console.log('MessageStore - WebSocket状态变更:', state)
      })

      wsService.on('onMessage', (message) => {
        handleIncomingMessage(message)
      })

      wsService.on('onSystemMessage', (message) => {
        handleIncomingSystemMessage(message)
      })

      wsService.on('onError', (errorMsg) => {
        error.value = errorMsg.message
        console.error('MessageStore - WebSocket错误:', errorMsg)
      })

      wsService.on('onHeartbeat', () => {
        console.log('MessageStore - 收到心跳响应')
      })

      // 在开始连接之前，确保状态同步
      console.log('MessageStore - 准备连接WebSocket')

      // 连接WebSocket
      await wsService.connect(token)
      console.log('MessageStore - WebSocket连接成功')

      // 延迟强制同步状态，确保组件能获取到正确的状态
      setTimeout(() => {
        console.log('MessageStore - 强制同步WebSocket状态')
        wsService.forceStateSync()
      }, 200)
    } catch (err) {
      console.error('WebSocket连接失败:', err)
      error.value = err instanceof Error ? err.message : '连接失败'
    }
  }

  // 处理接收到的消息
  const handleIncomingMessage = async (textMessage: TextMessage) => {
    console.log('🔍 [MessageStore] 开始处理接收到的消息:', textMessage)

    const message: Message = {
      id: textMessage.id,
      senderId: textMessage.senderId,
      receiverId: textMessage.receiverId,
      content: textMessage.content,
      timestamp: textMessage.timestamp,
      type: 1, // TEXT_MESSAGE
      isRead: false
    }

    const currentUserId = getCurrentUserId()
    console.log('🔍 [MessageStore] 当前用户ID:', currentUserId)
    console.log('🔍 [MessageStore] 消息发送者ID:', message.senderId)
    console.log('🔍 [MessageStore] 消息接收者ID:', message.receiverId)

    // 计算聊天用户ID
    const chatUserId = message.senderId === currentUserId ? message.receiverId : message.senderId
    console.log('🔍 [MessageStore] 计算的聊天用户ID:', chatUserId)
    console.log('🔍 [MessageStore] 当前聊天用户ID:', currentChatUserId.value)

    // 如果是自己发送的消息，检查是否有对应的临时消息需要替换
    if (message.senderId === currentUserId) {
      console.log('🔍 [handleIncomingMessage] 这是自己发送的消息，检查是否需要替换临时消息')
      const userMessages = messages.value.get(chatUserId) || []

      console.log(
        '🔍 [handleIncomingMessage] 当前聊天消息列表:',
        userMessages.map((msg) => ({
          id: msg.id,
          content: msg.content,
          isSending: msg.isSending,
          isTemp: msg.id.startsWith('temp_')
        }))
      )

      const tempMessageIndex = userMessages.findIndex(
        (msg) =>
          msg.id.startsWith('temp_') && // 查找临时消息
          msg.content === message.content &&
          msg.receiverId === message.receiverId &&
          msg.senderId === message.senderId
      )

      if (tempMessageIndex !== -1) {
        console.log(
          '🔍 [handleIncomingMessage] ✅ 找到对应的临时消息，进行替换:',
          userMessages[tempMessageIndex].id,
          '-> 服务器消息ID:',
          message.id
        )
        // 替换临时消息
        const finalMessage = {
          ...message,
          isSending: false
        }
        userMessages[tempMessageIndex] = finalMessage
        messages.value.set(chatUserId, userMessages)

        // 存储到数据库
        try {
          await dbManager.storeMessage(finalMessage, chatUserId)
          console.log(`🔍 [handleIncomingMessage] 替换的消息已存储到数据库: ${finalMessage.id}`)
        } catch (error) {
          console.error(
            `🔍 [handleIncomingMessage] 替换的消息存储到数据库失败: ${finalMessage.id}`,
            error
          )
        }

        // 更新聊天会话
        updateChatSession(chatUserId, finalMessage)
        console.log('🔍 [handleIncomingMessage] 临时消息替换完成，直接返回')
        return
      } else {
        console.log('🔍 [handleIncomingMessage] ❌ 没有找到对应的临时消息，将作为新消息添加')
      }
    }

    // 添加消息到对应的聊天（如果不是替换临时消息的情况）
    await addMessageToChat(chatUserId, message)

    // 更新聊天会话
    updateChatSession(chatUserId, message)

    console.log('🔍 [MessageStore] 消息处理完成:', message)
  }

  // 处理接收到的系统消息
  const handleIncomingSystemMessage = (systemMessage: SystemMessage) => {
    console.log('收到系统消息:', systemMessage)
    // 可以在这里添加系统消息的处理逻辑，比如显示通知等
    // 暂时只记录日志
  }

  // 发送消息
  const sendMessage = async (receiverId: string, content: string): Promise<boolean> => {
    if (!content.trim()) {
      error.value = '消息内容不能为空'
      return false
    }

    if (!wsService.isConnected()) {
      error.value = 'WebSocket未连接'
      return false
    }

    // 创建临时消息对象
    const tempMessage: Message = {
      id: `temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      senderId: getCurrentUserId(),
      receiverId,
      content: content.trim(),
      timestamp: Date.now(),
      type: 1,
      isSending: true
    }

    // 立即添加到界面显示
    addMessageToChat(receiverId, tempMessage)
    updateChatSession(receiverId, tempMessage)

    try {
      // 发送消息
      const success = wsService.sendTextMessage(receiverId, content.trim())

      if (success) {
        // 更新消息状态为已发送
        updateMessageStatus(receiverId, tempMessage.id, { isSending: false })
        return true
      } else {
        // 发送失败
        updateMessageStatus(receiverId, tempMessage.id, {
          isSending: false,
          sendError: '发送失败'
        })
        error.value = '消息发送失败'
        return false
      }
    } catch (err) {
      console.error('发送消息失败:', err)
      updateMessageStatus(receiverId, tempMessage.id, {
        isSending: false,
        sendError: '发送失败'
      })
      error.value = err instanceof Error ? err.message : '发送失败'
      return false
    }
  }

  // 获取分页状态
  const getPaginationState = (userId: string) => {
    return paginationState.value.get(userId) || { currentPage: 0, hasMore: true }
  }

  // 更新分页状态
  const updatePaginationState = (userId: string, page: number, hasMore: boolean) => {
    paginationState.value.set(userId, { currentPage: page, hasMore })
  }

  // 重置分页状态
  const resetPaginationState = (userId: string) => {
    paginationState.value.set(userId, { currentPage: 0, hasMore: true })
  }

  // 获取聊天历史
  const loadChatHistory = async (otherUserId: string, page = 1, limit = 50): Promise<boolean> => {
    if (isLoading.value) return false

    isLoading.value = true
    error.value = ''

    try {
      let loadedMessages: Message[] = []
      let fromCache = false

      // 首先检查本地数据库是否有历史消息
      try {
        const hasLocalMessages = await dbManager.hasMessages(otherUserId)
        console.log(
          `🔍 [loadChatHistory] 用户${otherUserId}本地数据库是否有消息:`,
          hasLocalMessages
        )

        if (hasLocalMessages) {
          // 从本地数据库加载消息
          console.log(`🔍 [loadChatHistory] 从本地数据库加载用户${otherUserId}的消息`)
          try {
            loadedMessages = await dbManager.getMessages(otherUserId, {
              limit,
              offset: (page - 1) * limit
            })
            fromCache = true
            console.log(`🔍 [loadChatHistory] 从数据库加载了${loadedMessages.length}条消息`)
          } catch (dbError) {
            console.error(`🔍 [loadChatHistory] 从数据库加载消息失败:`, dbError)
            // 数据库读取失败，降级到API加载
            console.log(`🔍 [loadChatHistory] 数据库读取失败，降级到API加载`)
          }
        }
      } catch (dbCheckError) {
        console.error(`🔍 [loadChatHistory] 检查数据库失败:`, dbCheckError)
        // 数据库检查失败，直接使用API
        console.log(`🔍 [loadChatHistory] 数据库检查失败，直接使用API`)
      }

      // 如果没有从缓存加载到消息，则从API获取
      if (!fromCache) {
        console.log(`🔍 [loadChatHistory] 从API获取用户${otherUserId}的历史消息`)
        try {
          const response = await apiClient.getChatHistory(otherUserId, page, limit)
          console.log(`API返回的聊天历史数据 (用户${otherUserId}):`, response)

          if (response && response.success) {
            // 处理API返回的消息，转换时间戳格式
            const processedMessages = (response.messages || []).map((msg) => ({
              ...msg,
              timestamp:
                typeof msg.timestamp === 'string'
                  ? new Date(msg.timestamp).getTime()
                  : msg.timestamp
            }))

            loadedMessages = processedMessages
            console.log(`🔍 [loadChatHistory] 从API加载了${loadedMessages.length}条消息`)

            // 批量存储到数据库（不阻塞主流程）
            if (loadedMessages.length > 0) {
              try {
                await dbManager.storeMessages(loadedMessages, otherUserId)
                console.log(`🔍 [loadChatHistory] 已将${loadedMessages.length}条消息存储到数据库`)
              } catch (storeError) {
                console.error(`🔍 [loadChatHistory] 批量存储消息到数据库失败:`, storeError)
                // 存储失败不影响消息显示
              }
            }
          } else {
            console.error(`获取聊天历史失败 - 用户${otherUserId}, API响应:`, response)
            error.value = response?.message || '获取聊天历史失败，请检查网络连接'
            return false
          }
        } catch (apiError) {
          console.error(`🔍 [loadChatHistory] API请求失败:`, apiError)
          error.value =
            apiError instanceof Error ? apiError.message : '网络请求失败，请检查网络连接'
          return false
        }
      }

      // 将消息添加到store
      const existingMessages = messages.value.get(otherUserId) || []
      const newMessages = loadedMessages.filter(
        (msg) => !existingMessages.some((existing) => existing.id === msg.id)
      )

      console.log(`🔍 [loadChatHistory] 过滤后的新消息数量:`, newMessages.length)

      if (newMessages.length > 0) {
        const allMessages = [...newMessages, ...existingMessages].sort(
          (a, b) => a.timestamp - b.timestamp
        )
        messages.value.set(otherUserId, allMessages)

        // 更新分页状态 - 如果加载的消息数量等于限制数量，说明可能还有更多消息
        updatePaginationState(otherUserId, page, newMessages.length >= limit)
        console.log(
          `🔍 [loadChatHistory] 更新分页状态: 用户${otherUserId}, 当前页${page}, 还有更多消息: ${newMessages.length >= limit}`
        )

        // 更新聊天会话
        const lastMessage = allMessages[allMessages.length - 1]
        if (lastMessage) {
          console.log(
            `🔍 [loadChatHistory] 更新聊天会话 (用户${otherUserId}), 最后消息:`,
            lastMessage
          )
          updateChatSession(otherUserId, lastMessage)
        }
      } else {
        // 没有新消息，说明没有更多了
        updatePaginationState(otherUserId, page, false)
        console.log(
          `🔍 [loadChatHistory] 没有新消息，更新分页状态: 用户${otherUserId}, 当前页${page}, 没有更多消息`
        )
        // 即使没有消息，也要确保创建聊天会话（避免显示"暂无消息"）
        console.log(`🔍 [loadChatHistory] 用户${otherUserId}没有新消息，检查是否需要创建空会话`)
        const existingSession = chatSessions.value.get(otherUserId)
        if (!existingSession) {
          console.log(`🔍 [loadChatHistory] 为用户${otherUserId}创建空的聊天会话`)
          // 创建一个空的聊天会话
          try {
            const userDetail = await apiClient.getUserDetail(otherUserId)
            if (userDetail.success) {
              const emptySession = {
                userId: otherUserId,
                userName: userDetail.user.displayName,
                userAvatar: userDetail.user.avatar,
                lastMessage: undefined,
                unreadCount: 0,
                lastActiveTime: Date.now()
              }
              chatSessions.value.set(otherUserId, emptySession)
              console.log(
                `🔍 [loadChatHistory] 空聊天会话已创建 - 用户${otherUserId}:`,
                emptySession
              )
            }
          } catch (err) {
            console.error('获取用户信息失败:', err)
          }
        } else {
          console.log(`🔍 [loadChatHistory] 用户${otherUserId}已有聊天会话，无需创建`)
        }
      }

      console.log(
        `🔍 [loadChatHistory] 加载${otherUserId}的聊天历史成功，共${newMessages.length}条新消息`
      )
      console.log(
        `🔍 [loadChatHistory] 用户${otherUserId}的聊天会话状态:`,
        chatSessions.value.get(otherUserId)
      )
      return true
    } catch (err) {
      console.error('获取聊天历史失败:', err)
      error.value = err instanceof Error ? err.message : '获取聊天历史失败'
      return false
    } finally {
      isLoading.value = false
    }
  }

  // 添加消息到聊天（带去重逻辑）
  const addMessageToChat = async (userId: string, message: Message) => {
    const userMessages = messages.value.get(userId) || []

    // 检查消息是否已存在，避免重复添加
    const existingMessage = userMessages.find((msg) => msg.id === message.id)
    if (existingMessage) {
      console.log('🔍 [addMessageToChat] ❌ 消息已存在，跳过添加:', message.id)
      return
    }

    console.log(
      '🔍 [addMessageToChat] ✅ 添加新消息:',
      message.id,
      message.content.substring(0, 20),
      '发送状态:',
      message.isSending ? '发送中' : '已发送',
      '错误状态:',
      message.sendError || '无'
    )
    userMessages.push(message)
    messages.value.set(userId, userMessages)

    // 如果不是临时消息，则存储到数据库
    if (!message.id.startsWith('temp_') && !message.isSending) {
      try {
        await dbManager.storeMessage(message, userId)
        console.log(`🔍 [addMessageToChat] 消息已存储到数据库: ${message.id}`)
      } catch (error) {
        console.error(`🔍 [addMessageToChat] 消息存储到数据库失败: ${message.id}`, error)
      }
    }

    console.log(`🔍 [addMessageToChat] 当前用户 ${userId} 的消息总数:`, userMessages.length)
    console.log(`🔍 [addMessageToChat] 当前聊天用户ID:`, currentChatUserId.value)
  }

  // 更新消息状态
  const updateMessageStatus = (userId: string, messageId: string, updates: Partial<Message>) => {
    const userMessages = messages.value.get(userId)
    if (userMessages) {
      const messageIndex = userMessages.findIndex((msg) => msg.id === messageId)
      if (messageIndex !== -1) {
        console.log(`🔍 [updateMessageStatus] 更新消息状态:`, messageId, '更新内容:', updates)
        Object.assign(userMessages[messageIndex], updates)
        console.log(`🔍 [updateMessageStatus] 更新后的消息:`, userMessages[messageIndex])
      } else {
        console.log(`🔍 [updateMessageStatus] ❌ 未找到消息:`, messageId)
      }
    } else {
      console.log(`🔍 [updateMessageStatus] ❌ 未找到用户消息列表:`, userId)
    }
  }

  // 更新聊天会话
  const updateChatSession = async (userId: string, lastMessage: Message) => {
    console.log(`开始更新聊天会话 - 用户ID: ${userId}, 消息:`, lastMessage)
    let session = chatSessions.value.get(userId)

    if (!session) {
      // 创建新的聊天会话，需要获取用户信息
      try {
        const userDetail = await apiClient.getUserDetail(userId)
        if (userDetail.success) {
          session = {
            userId,
            userName: userDetail.user.displayName,
            userAvatar: userDetail.user.avatar,
            lastMessage,
            unreadCount: 0,
            lastActiveTime: lastMessage.timestamp
          }
        } else {
          // 如果获取用户信息失败，使用默认信息
          session = {
            userId,
            userName: `用户${userId}`,
            userAvatar: '/avatars/default.png',
            lastMessage,
            unreadCount: 0,
            lastActiveTime: lastMessage.timestamp
          }
        }
      } catch (err) {
        console.error('获取用户信息失败:', err)
        session = {
          userId,
          userName: `用户${userId}`,
          userAvatar: '/avatars/default.png',
          lastMessage,
          unreadCount: 0,
          lastActiveTime: lastMessage.timestamp
        }
      }
    } else {
      // 更新现有会话
      session.lastMessage = lastMessage
      session.lastActiveTime = lastMessage.timestamp
    }

    // 如果不是当前聊天用户且消息不是自己发送的，增加未读计数
    const currentUserId = getCurrentUserId()
    const isCurrentChatUser = userId === currentChatUserId.value
    const isOwnMessage = lastMessage.senderId === currentUserId

    console.log('🔍 [UpdateSession] 未读计数检查:')
    console.log('  - 用户ID:', userId)
    console.log('  - 当前聊天用户ID:', currentChatUserId.value)
    console.log('  - 是当前聊天用户:', isCurrentChatUser)
    console.log('  - 消息发送者ID:', lastMessage.senderId)
    console.log('  - 当前用户ID:', currentUserId)
    console.log('  - 是自己的消息:', isOwnMessage)
    console.log('  - 更新前未读数量:', session.unreadCount)

    if (!isCurrentChatUser && !isOwnMessage) {
      session.unreadCount++
      console.log('🔍 [UpdateSession] ✅ 增加未读计数，新数量:', session.unreadCount)
    } else {
      console.log(
        '🔍 [UpdateSession] ❌ 不增加未读计数，原因:',
        isCurrentChatUser ? '是当前聊天用户' : '是自己的消息'
      )
    }

    chatSessions.value.set(userId, session)
    console.log(`🔍 [UpdateSession] 聊天会话已更新 - 用户ID: ${userId}, 会话:`, session)
    console.log(`🔍 [UpdateSession] 当前总未读数量:`, totalUnreadCount.value)
  }

  // 设置当前聊天用户
  const setCurrentChatUser = (userId: string) => {
    currentChatUserId.value = userId

    // 清除该用户的未读计数
    const session = chatSessions.value.get(userId)
    if (session) {
      session.unreadCount = 0
      chatSessions.value.set(userId, session)
    }

    // 标记该用户的所有消息为已读
    const userMessages = messages.value.get(userId)
    if (userMessages) {
      userMessages.forEach((msg) => {
        if (msg.senderId !== getCurrentUserId()) {
          msg.isRead = true
        }
      })
    }
  }

  // 断开WebSocket连接
  const disconnectWebSocket = () => {
    wsService.disconnect()
    wsState.value = WebSocketState.DISCONNECTED
  }

  // 手动重连WebSocket
  const manualReconnectWebSocket = () => {
    wsService.manualReconnect()
  }

  // 清除错误
  const clearError = () => {
    error.value = ''
  }

  // 批量设置聊天会话（用于从API加载的联系人数据）
  const setChatSessions = (sessions: ChatSession[]) => {
    console.log('MessageStore - 设置聊天会话，数量:', sessions.length)
    console.log('MessageStore - 会话数据:', sessions)
    sessions.forEach((session) => {
      chatSessions.value.set(session.userId, session)
    })
    console.log('MessageStore - 设置完成，当前会话总数:', chatSessions.value.size)
  }

  // 获取当前用户ID
  const getCurrentUserId = (): string => {
    const user = apiClient.getCurrentUser()
    return user?.id || ''
  }

  // 重试发送失败的消息
  const retryMessage = async (userId: string, messageId: string): Promise<boolean> => {
    const userMessages = messages.value.get(userId)
    if (!userMessages) return false

    const message = userMessages.find((msg) => msg.id === messageId)
    if (!message || !message.sendError) return false

    // 清除错误状态，设置为发送中
    updateMessageStatus(userId, messageId, {
      sendError: undefined,
      isSending: true
    })

    // 重新发送
    const success = wsService.sendTextMessage(message.receiverId, message.content)

    if (success) {
      updateMessageStatus(userId, messageId, { isSending: false })
      return true
    } else {
      updateMessageStatus(userId, messageId, {
        isSending: false,
        sendError: '重发失败'
      })
      return false
    }
  }

  return {
    // 状态
    messages,
    chatSessions,
    currentChatUserId,
    wsState: webSocketStore.state, // 使用全局 WebSocket 状态
    isLoading,
    error,
    paginationState,

    // 计算属性
    currentMessages,
    sortedChatSessions,
    totalUnreadCount,

    // 方法
    initWebSocket,
    sendMessage,
    loadChatHistory,
    setCurrentChatUser,
    disconnectWebSocket,
    manualReconnectWebSocket,
    clearError,
    retryMessage,
    setChatSessions,
    getPaginationState,
    updatePaginationState,
    resetPaginationState,

    // 暴露 WebSocket store 的方法
    webSocketStore
  }
})
