<!-- 消息列表组件 -->
<template>
  <div ref="messagesArea" class="flex-1 overflow-y-auto p-4 custom-scrollbar">
    <!-- 未选择联系人时的占位符 -->
    <div
      v-if="!currentContact"
      class="flex flex-col items-center justify-center h-full text-gray-500"
    >
      <div class="w-40 h-40 mb-6">
        <img src="../assets/editor/chat-placeholder.svg" alt="选择聊天" class="w-full h-full" />
      </div>
      <p class="text-sm font-medium">请选择一个聊天开始对话</p>
    </div>
    <!-- 已选择联系人但无消息 -->
    <div
      v-else-if="!messages.length"
      class="flex items-center justify-center h-full text-gray-500 text-sm"
    >
      暂无消息
    </div>
    <div v-else>
      <!-- 加载更多消息的指示器 -->
      <div v-if="isLoadingMore" class="flex items-center justify-center py-4 text-gray-500">
        <div
          class="w-4 h-4 border border-gray-400 border-t-transparent rounded-full animate-spin mr-2"
        ></div>
        <span class="text-sm">正在加载更多消息...</span>
      </div>

      <!-- 没有更多消息的提示 -->
      <div
        v-else-if="!hasMoreMessages && !isInitialLoad"
        class="flex items-center justify-center py-4 text-gray-400"
      >
        <span class="text-xs">没有更多历史消息了</span>
      </div>

      <div v-for="message in messages" :key="message.id" class="mb-4">
        <!-- 判断是否为当前用户发送的消息 -->
        <div v-if="isCurrentUserMessage(message)" class="flex items-start gap-3 justify-end">
          <!-- 重试按钮，在发送失败时显示 -->
          <div v-if="message.sendError" class="flex items-center">
            <button
              @click="retryMessage(message)"
              class="w-6 h-6 bg-red-500 hover:bg-red-600 text-white rounded-full flex items-center justify-center transition-colors duration-200"
              title="重新发送"
            >
              <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                <path
                  fill-rule="evenodd"
                  d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z"
                  clip-rule="evenodd"
                />
              </svg>
            </button>
          </div>

          <!-- 当前用户消息：内容在左，头像在右 -->
          <div class="flex-1 flex flex-col items-end">
            <div class="flex items-center gap-2 mb-2 text-right">
              <!-- 发送状态指示 -->
              <div v-if="message.isSending" class="flex items-center gap-1">
                <div
                  class="w-3 h-3 border border-gray-400 border-t-transparent rounded-full animate-spin"
                ></div>
                <span class="text-xs text-gray-400">发送中...</span>
              </div>
              <div v-else-if="message.sendError" class="flex items-center gap-1">
                <svg class="w-3 h-3 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                  <path
                    fill-rule="evenodd"
                    d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                    clip-rule="evenodd"
                  />
                </svg>
                <span class="text-xs text-red-500">发送失败</span>
              </div>
              <span class="text-xs text-gray-400 font-normal">
                {{ formatMessageTime(message.timestamp) }}
              </span>
              <span class="text-xs text-gray-600 font-medium">{{ message.senderName }}</span>
            </div>
            <div
              :class="[
                'rounded-lg p-3 max-w-md shadow-sm',
                message.sendError ? 'bg-red-50 border border-red-200' : 'bg-[#c9daf5] text-white'
              ]"
            >
              <div
                :class="[
                  'text-sm leading-relaxed break-words whitespace-pre-wrap',
                  message.sendError ? 'text-red-700' : 'text-gray-900'
                ]"
                v-text="message.content"
              ></div>
            </div>
          </div>
          <UserDetailPopover
            :user-id="getCurrentUserId(message)"
            v-model:open="popoverStates[message.id + '_current']"
          >
            <UserAvatar
              :name="message.senderName"
              size="medium"
              class="cursor-pointer hover:opacity-80 transition-opacity"
            />
          </UserDetailPopover>
        </div>

        <!-- 其他用户消息：头像在左，内容在右 -->
        <div v-else class="flex items-start gap-3">
          <UserDetailPopover :user-id="message.senderId" v-model:open="popoverStates[message.id]">
            <UserAvatar
              :name="message.senderName"
              size="medium"
              class="cursor-pointer hover:opacity-80 transition-opacity"
            />
          </UserDetailPopover>
          <div class="flex flex-col">
            <div class="flex items-center gap-2 mb-2">
              <span class="text-xs text-gray-600 font-medium">{{ message.senderName }}</span>
              <span class="text-xs text-gray-400 font-normal">
                {{ formatMessageTime(message.timestamp) }}
              </span>
            </div>
            <div class="bg-[#ffffff] border border-gray-200 rounded-lg p-3 shadow-sm max-w-md">
              <div
                class="text-sm text-gray-900 leading-relaxed break-words whitespace-pre-wrap"
                v-text="message.content"
              ></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick, watch, reactive, onMounted, onUnmounted, computed } from 'vue'
import UserAvatar from './UserAvatar.vue'
import UserDetailPopover from './UserDetailPopover.vue'
import { useUserStore } from '../store/user'
import { useMessageStore } from '../store/message'
import { notificationService } from '../services/notificationService'

import type { User } from '../api'

interface Message {
  id: string
  senderId: string
  senderName: string
  content: string
  timestamp: Date
  isSending?: boolean
  sendError?: string
}

interface Contact {
  id: string
  name: string
  avatar: string
  status: string
  lastMessage: string
  lastMessageTime: Date
  user: User
  hasLastMessage?: boolean
}

interface Props {
  messages: Message[]
  currentContact: Contact | null
}

const props = defineProps<Props>()

// 用户状态管理
const userStore = useUserStore()
const messageStore = useMessageStore()

const messagesArea = ref<HTMLElement>()

// 管理每个消息的 Popover 状态
const popoverStates = reactive<Record<string, boolean>>({})

// 分页相关状态
const pageSize = ref(50)
const isLoadingMore = ref(false)
const isInitialLoad = ref(true)

// 从 store 获取分页状态
const currentPaginationState = computed(() => {
  if (!props.currentContact) return { currentPage: 0, hasMore: true }
  return messageStore.getPaginationState(props.currentContact.id)
})

const hasMoreMessages = computed(() => currentPaginationState.value.hasMore)

// 判断是否为当前用户发送的消息
const isCurrentUserMessage = (message: Message) => {
  const currentUserId = userStore.currentUser.value?.id
  return message.senderId === currentUserId
}

// 获取当前用户的真实 ID
const getCurrentUserId = (message: Message) => {
  if (isCurrentUserMessage(message)) {
    return userStore.currentUser?.id || null
  }
  return message.senderId
}

// 初始化 Popover 状态
watch(
  () => props.messages,
  (newMessages) => {
    newMessages.forEach((message) => {
      // 为其他用户消息创建 Popover 状态
      if (!(message.id in popoverStates)) {
        popoverStates[message.id] = false
      }
      // 为当前用户消息创建 Popover 状态
      const currentUserKey = message.id + '_current'
      if (!(currentUserKey in popoverStates)) {
        popoverStates[currentUserKey] = false
      }
    })
  },
  { immediate: true, deep: true }
)

// 滚动到底部
const scrollToBottom = () => {
  if (messagesArea.value) {
    messagesArea.value.scrollTop = messagesArea.value.scrollHeight
  }
}

// 检测是否滚动到顶部
const isScrolledToTop = () => {
  if (!messagesArea.value) return false
  return messagesArea.value.scrollTop <= 50 // 50px 的缓冲区
}

// 滚动事件处理
const handleScroll = async () => {
  if (!props.currentContact) {
    console.log('🔍 [handleScroll] 没有选中的联系人')
    return
  }

  if (isLoadingMore.value) {
    console.log('🔍 [handleScroll] 正在加载中，跳过')
    return
  }

  if (!hasMoreMessages.value) {
    console.log('🔍 [handleScroll] 没有更多消息，跳过')
    return
  }

  if (isScrolledToTop()) {
    console.log('🔍 [handleScroll] 检测到滚动到顶部，开始加载更多消息')
    await loadMoreMessages()
  }
}

// 加载更多消息
const loadMoreMessages = async () => {
  if (!props.currentContact || isLoadingMore.value || !hasMoreMessages.value) {
    return
  }

  isLoadingMore.value = true

  try {
    // 记录当前滚动位置和消息数量
    const previousScrollHeight = messagesArea.value?.scrollHeight || 0
    const previousMessageCount = props.messages.length

    // 加载下一页消息 - 确保不重复加载第1页
    const currentPage = currentPaginationState.value.currentPage
    const nextPage = Math.max(currentPage + 1, 2) // 至少从第2页开始加载
    console.log(`🔍 [loadMoreMessages] 当前页: ${currentPage}, 加载第${nextPage}页消息`)

    const success = await messageStore.loadChatHistory(
      props.currentContact.id,
      nextPage,
      pageSize.value
    )

    if (success) {
      const newMessageCount = props.messages.length
      const loadedCount = newMessageCount - previousMessageCount

      console.log(`🔍 [loadMoreMessages] 成功加载${loadedCount}条新消息`)
      console.log(
        `🔍 [loadMoreMessages] 消息数量变化: ${previousMessageCount} -> ${newMessageCount}`
      )
      console.log(`🔍 [loadMoreMessages] 更新后的分页状态:`, currentPaginationState.value)

      if (loadedCount > 0) {
        // 保持滚动位置
        await nextTick()
        if (messagesArea.value) {
          const newScrollHeight = messagesArea.value.scrollHeight
          const heightDifference = newScrollHeight - previousScrollHeight
          messagesArea.value.scrollTop = heightDifference
          console.log(`🔍 [loadMoreMessages] 滚动位置调整: ${heightDifference}px`)
        }
      } else {
        console.log(`🔍 [loadMoreMessages] 没有加载到新消息，可能已经没有更多历史消息`)
      }
    } else {
      console.error(`🔍 [loadMoreMessages] 加载第${nextPage}页消息失败`)
      notificationService.error('加载历史消息失败，请稍后重试')
    }
  } catch (error) {
    console.error('加载更多消息失败:', error)
    notificationService.error('加载历史消息失败')
  } finally {
    isLoadingMore.value = false
  }
}

// 重置分页状态（当切换联系人时）
const resetPaginationState = () => {
  if (props.currentContact) {
    messageStore.resetPaginationState(props.currentContact.id)
  }
  isLoadingMore.value = false
  isInitialLoad.value = true
}

// 监听联系人变化，重置分页状态
watch(
  () => props.currentContact?.id,
  (newContactId, oldContactId) => {
    if (newContactId !== oldContactId) {
      resetPaginationState()
    }
  }
)

// 监听消息变化，自动滚动到底部（仅在初始加载或新消息时）
watch(
  () => props.messages,
  (newMessages, oldMessages) => {
    nextTick(() => {
      // 如果是初始加载或者消息数量增加（新消息），则滚动到底部
      if (isInitialLoad.value || (oldMessages && newMessages.length > oldMessages.length)) {
        scrollToBottom()
        if (isInitialLoad.value) {
          isInitialLoad.value = false
        }
      }
    })
  },
  { deep: true }
)

// 添加 ResizeObserver 来监听容器高度变化
let resizeObserver: ResizeObserver | null = null

onMounted(() => {
  // 监听容器高度变化
  if (messagesArea.value) {
    resizeObserver = new ResizeObserver(() => {
      nextTick(() => {
        scrollToBottom()
      })
    })
    resizeObserver.observe(messagesArea.value)

    // 添加滚动事件监听器
    messagesArea.value.addEventListener('scroll', handleScroll)
  }
})

onUnmounted(() => {
  if (resizeObserver) {
    resizeObserver.disconnect()
  }

  // 移除滚动事件监听器
  if (messagesArea.value) {
    messagesArea.value.removeEventListener('scroll', handleScroll)
  }
})

// 时间格式化函数
const formatMessageTime = (date: Date) => {
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 重试发送失败的消息
const retryMessage = async (message: Message) => {
  if (!props.currentContact || !message.sendError) {
    return
  }

  try {
    const success = await messageStore.retryMessage(props.currentContact.id, message.id)
    if (success) {
      notificationService.success('消息重发成功')
    } else {
      notificationService.error('消息重发失败，请稍后重试')
    }
  } catch (error) {
    console.error('重试消息失败:', error)
    notificationService.error('消息重发失败')
  }
}

// 暴露滚动方法给父组件
defineExpose({
  scrollToBottom
})
</script>

<style scoped>
/* 自定义滚动条样式 */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f1f1; /* 滚动条轨道背景色 */
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #c1c1c1; /* 滚动条滑块背景色 */
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8; /* 滚动条滑块悬停背景色 */
}
</style>
